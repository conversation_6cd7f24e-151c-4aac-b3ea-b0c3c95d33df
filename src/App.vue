<template>
  <!-- 顶部一个生成按钮,每次点击都会生成不同的五个题目,以及对应的二维码答案.
  以下内容题目内容,需要可以被到处成pdf. -->

  <!-- 控制按钮区域 -->
  <div class="control-panel">
    <button @click="generateNewProblems" class="generate-btn">生成新题目</button>
    <button @click="exportToPDF" class="export-btn">导出PDF</button>
  </div>

  <div class="problem" id="pdf-content">
    <div class="title">
      <div class="centered-content">
        <h1>小学乘法练习</h1>
        <span>姓名: _____________ 日期: _____________ 用时: _____________</span><br><br>
      </div>
      <span>一、列竖式计算</span><br>
    </div>

    <!-- 页面右上角要显示一个二维码,扫码可以看到 所有题目的答案. -->
    <div class="qr-code-container">
      <canvas ref="qrCanvas" class="qr-code"></canvas>
      <span class="qr-label">扫码查看答案</span>
    </div>

    <div class="waterfall-container">
      <Multiplication v-for="(item, index) in problems" :key="index" :first="item.first" :second="item.second" />
    </div>
  </div>
</template>

<script>
  import Multiplication from './components/Multiplication.vue';
  import QRCode from 'qrcode';
  import html2pdf from 'html2pdf.js';

  export default {
    components: { Multiplication },
    data() {
      return {
        problems: [
          { first: 154, second: 803 },
          { first: 756, second: 943 },
          { first: 549, second: 851 },
          { first: 756, second: 943 },
          { first: 549, second: 851 },
        ]
      };
    },
    mounted() {
      this.generateQRCode();
    },
    methods: {
      // 生成随机的乘法题目
      generateNewProblems() {
        this.problems = [];
        for (let i = 0; i < 5; i++) {
          const first = Math.floor(Math.random() * 900) + 100; // 100-999
          const second = Math.floor(Math.random() * 900) + 100; // 100-999
          this.problems.push({ first, second });
        }
        // 重新生成二维码
        this.$nextTick(() => {
          this.generateQRCode();
        });
      },

      // 生成包含答案的二维码
      generateQRCode() {
        const answers = this.problems.map((problem, index) => {
          return `题目${index + 1}: ${problem.first} × ${problem.second} = ${problem.first * problem.second}`;
        }).join('\n');

        const qrText = `小学乘法练习答案:\n${answers}`;

        QRCode.toCanvas(this.$refs.qrCanvas, qrText, {
          width: 120,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        }, (error) => {
          if (error) console.error('QR Code generation failed:', error);
        });
      },

      // 导出为PDF
      exportToPDF() {
        const element = document.getElementById('pdf-content');
        const opt = {
          margin: 1,
          filename: `小学乘法练习_${new Date().toLocaleDateString()}.pdf`,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2 },
          jsPDF: { unit: 'in', format: 'letter', orientation: 'portrait' }
        };

        html2pdf().set(opt).from(element).save();
      }
    }
  }
</script>

<style scoped>
  .control-panel {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1000;
    display: flex;
    gap: 10px;
  }

  .generate-btn, .export-btn {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
  }

  .generate-btn:hover, .export-btn:hover {
    background-color: #0056b3;
  }

  .problem {
    position: relative;
    padding-top: 60px; /* 为顶部按钮留出空间 */
  }

  .title {
    margin-left: 20px;
  }

  .centered-content {
    text-align: center;
  }

  .qr-code-container {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .qr-code {
    margin-bottom: 5px;
  }

  .qr-label {
    font-size: 12px;
    color: #666;
    text-align: center;
  }

  .waterfall-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-evenly;
    margin-top: 20px;
  }

  .waterfall-container>* {
    break-inside: avoid;
    margin-bottom: 20px;
  }

  /* 打印时隐藏控制按钮 */
  @media print {
    .control-panel {
      display: none;
    }
    .problem {
      padding-top: 0;
    }
  }
</style>