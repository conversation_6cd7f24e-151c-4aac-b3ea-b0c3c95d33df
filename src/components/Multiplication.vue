<template>
  <div class="multiplication-module" style="text-align: center; font-family: Arial, sans-serif;">
    {{ first }} × {{ second }}=<br>
    <div style="position: relative; width: 200px; height: 200px; margin: 0 auto;">
      <div style="border-top: 1px dashed black; position: absolute; top: 0; left: -40px; width: 280px;"></div>
      <div style="position: absolute; top: 0; left: 0; display: flex; justify-content: space-between; width: 100%; height: 100%;">
        <div style="border-left: 1px dashed black; height: 100%;"></div>
        <div style="border-left: 1px dashed black; height: 100%;"></div>
        <div style="border-left: 1px dashed black; height: 100%;"></div>
        <div style="border-left: 1px dashed black; height: 100%;"></div>
        <div style="border-left: 1px dashed black; height: 100%;"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultiplicationModule',
  props: {
    first: {
      type: [Number, String],
      default: 154
    },
    second: {
      type: [Number, String],
      default: 803
    },
    answer: {
      type: [Number, String],
      default: 123692
    }
  }
}
</script>

<style scoped>
.multiplication-module {
  margin: 20px 70px;
}
</style>