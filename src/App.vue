<template>
  <!-- 顶部一个生成按钮,每次点击都会生成不同的五个题目,以及对应的二维码答案.
  以下内容题目内容,需要可以被到处成pdf. -->


  <div class="problem">
    <div class="title">
      <h1>小学乘法练习</h1>
      <span>姓名: _____________ 日期: _____________ 用时: _____________</span><br><br>
      <span>一、列竖式计算</span><br>
    </div>
    <!-- // 页面右上角要显示一个二维码,扫码可以看到 所有题目的答案. -->
    <div class="waterfall-container">
      <Multiplication v-for="(item, index) in problems" :key="index" :first="item.first" :second="item.second" />
    </div>
  </div>
</template>

<script>
  import Multiplication from './components/Multiplication.vue';

  export default {
    components: { Multiplication },
    data() {
      return {
        problems: [
          { first: 154, second: 803 },
          { first: 756, second: 943 },
          { first: 549, second: 851 },
          // 添加更多题目...
        ]
      };
    }
  }
</script>

<style scoped>
  .title {
    text-align: center;
    margin-bottom: 20px;
  }

  .waterfall-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .waterfall-container>* {
    break-inside: avoid;
    margin-bottom: 20px;
  }
</style>